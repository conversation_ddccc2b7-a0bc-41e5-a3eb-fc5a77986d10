name: pandoo_delivery
description: Pandoo Delivery - Customer App


publish_to: 'none' # Remove this line if you wish to publish to pub.dev


version: 1.0.0+1

environment:
  sdk: '>=3.5.0 <4.0.0'


dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  #base:
  dio: ^5.7.0
  intl: ^0.20.2
  get_it: ^8.0.0
  google_fonts: ^6.2.1
  pull_to_refresh: ^2.0.0
  flutter_screenutil: ^5.9.0
  proste_indexed_stack: ^0.2.4


  http: ^1.2.2
  crypto: ^3.0.6
  auto_route: ^9.2.0
  flutter_bloc: ^8.1.3
  path_provider: ^2.1.2
  flutter_riverpod: ^2.5.1
  freezed_annotation: ^2.4.4
  shared_preferences: ^2.3.2
  permission_handler: ^11.3.1
  flutter_displaymode: ^0.6.0

  #ui
  jiffy: ^6.2.2
  lottie: ^3.0.0
  confetti: ^0.8.0
  flutter_svg: ^2.0.9
  share_plus: ^10.1.1
  card_swiper: ^3.0.1
  sms_autofill: ^2.2.0
  url_launcher: ^6.3.0
  uni_links: ^0.5.1
  flutter_remix: ^0.0.3
  webview_flutter: ^4.10.0
  intl_phone_field: ^3.2.0
  connectivity_plus: ^6.0.5
  sliding_up_panel: ^2.0.0+1
  flutter_rating_bar: ^4.0.1
  top_snackbar_flutter: ^3.1.0
  flutter_masked_text2: ^0.9.1
  flutter_html: ^3.0.0-alpha.6
  flutter_native_splash: ^2.4.2
  smooth_page_indicator: ^1.2.0
  visibility_detector: ^0.4.0+2
  flutter_swipe_detector: ^2.0.0
  flutter_advanced_switch: ^3.0.1
  flutter_carousel_slider: ^1.1.0
  floating_draggable_widget: ^2.1.5
  flutter_staggered_grid_view: ^0.7.0
  flutter_staggered_animations: ^1.1.1
  app_tracking_transparency: ^2.0.6

  #media:
  image: ^4.1.7
  file_picker: ^8.0.7
  image_picker: ^1.0.7
  image_cropper: ^8.0.2
  cached_network_image: ^3.3.1

  #maps:
  geocoding: ^3.0.0
  geolocator: ^13.0.1
  google_place:
    git:
      url: https://github.com/Muhammadyunusxon/google_place.git
  osm_nominatim: ^3.0.1
  google_maps_flutter: ^2.9.0

  #auth:
  google_sign_in: ^6.2.1
  sign_in_with_apple: ^6.1.2
  flutter_facebook_auth: ^7.0.0

  #firebase:
  firebase_messaging: ^15.1.0
  firebase_core: ^3.4.0
  firebase_auth: ^5.2.0
  cloud_firestore: ^5.4.0

  payfast:
    git:
      url: https://github.com/Muhammadyunusxon/payfast_flutter.git

dev_dependencies:
  flutter_test:
    sdk: flutter
  freezed: ^2.5.7
  build_runner: ^2.4.12
  flutter_lints: ^5.0.0
  auto_route_generator: ^9.0.0
  flutter_launcher_icons: ^0.14.1

flutter:

  uses-material-design: true
  assets:
    - assets/images/
    - assets/svgs/
    - assets/lottie/
    - assets/
    - assets/images/ui3.png

flutter_icons:
  android: true
  ios: true
  remove_alpha_ios: true
  image_path: 'assets/images/app_logo.png'